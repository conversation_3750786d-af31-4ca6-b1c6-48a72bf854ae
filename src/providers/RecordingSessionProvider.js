/**
 * RecordingSessionProvider - Isolated Auto-Advance Logic
 * Handles recording session state, phrase progression, and auto-advance functionality
 * This provider isolates the complex auto-advance logic to prevent race conditions
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import { phraseCollectionConfig } from '../phrases';
import {
  getSelectedPhrases,
  saveSelectedPhrases
} from '../services/phraseRotationService';

// Action types
const RECORDING_ACTIONS = {
  SET_SELECTED_PHRASES: 'SET_SELECTED_PHRASES',
  SET_CURRENT_PHRASE_INDEX: 'SET_CURRENT_PHRASE_INDEX',
  SET_CURRENT_RECORDING_NUMBER: 'SET_CURRENT_RECORDING_NUMBER',
  SET_SELECTED_CATEGORY: 'SET_SELECTED_CATEGORY',
  UPDATE_RECORDINGS_COUNT: 'UPDATE_RECORDINGS_COUNT',
  SET_COMPLETION_PROMPT: 'SET_COMPLETION_PROMPT',
  RESET_RECORDING_SESSION: 'RESET_RECORDING_SESSION',
  SET_PHRASES_SELECTED: 'SET_PHRASES_SELECTED',
  SET_INITIALIZATION_COMPLETE: 'SET_INITIALIZATION_COMPLETE',
  SET_RECORDING_IN_PROGRESS: 'SET_RECORDING_IN_PROGRESS'
};

// Initial state
const initialState = {
  selectedPhrases: null,
  currentPhraseIndex: 0,
  currentRecordingNumber: 1,
  selectedCategory: '',
  recordingsCount: {},
  showCompletionPrompt: false,
  phrasesSelected: false,
  isInitializing: true, // Prevent auto-advancement during initialization
  recordingInProgress: false, // Track if user is actively recording
  hasActiveRecordings: false, // Track if user has made recordings in this session

  // Configuration
  RECORDINGS_PER_PHRASE: phraseCollectionConfig.recordingsPerPhrase,
  COLLECTION_GOAL: phraseCollectionConfig.collectionGoal
};

// Reducer with focused state management
const recordingSessionReducer = (state, action) => {
  switch (action.type) {
    case RECORDING_ACTIONS.SET_SELECTED_PHRASES:
      return {
        ...state,
        selectedPhrases: action.payload,
        currentPhraseIndex: 0,
        selectedCategory: action.payload?.[0]?.category || ''
      };
      
    case RECORDING_ACTIONS.SET_CURRENT_PHRASE_INDEX:
      return {
        ...state,
        currentPhraseIndex: action.payload
      };
      
    case RECORDING_ACTIONS.SET_CURRENT_RECORDING_NUMBER:
      return {
        ...state,
        currentRecordingNumber: action.payload
      };
      
    case RECORDING_ACTIONS.SET_SELECTED_CATEGORY:
      return {
        ...state,
        selectedCategory: action.payload
      };
      
    case RECORDING_ACTIONS.UPDATE_RECORDINGS_COUNT:
      const newRecordingsCount = {
        ...state.recordingsCount,
        [action.payload.phraseKey]: action.payload.count
      };
      
      // Persist to localStorage
      try {
        localStorage.setItem('icuAppRecordingsCount', JSON.stringify(newRecordingsCount));
      } catch (error) {
        console.warn('⚠️ Failed to save recording counts to localStorage:', error);
      }
      
      return {
        ...state,
        recordingsCount: newRecordingsCount
      };
      
    case RECORDING_ACTIONS.SET_COMPLETION_PROMPT:
      return {
        ...state,
        showCompletionPrompt: action.payload
      };
      
    case RECORDING_ACTIONS.SET_PHRASES_SELECTED:
      return {
        ...state,
        phrasesSelected: action.payload
      };
      
    case RECORDING_ACTIONS.RESET_RECORDING_SESSION:
      return {
        ...initialState,
        recordingsCount: state.recordingsCount // Preserve recording counts
      };

    case RECORDING_ACTIONS.SET_INITIALIZATION_COMPLETE:
      return {
        ...state,
        isInitializing: false
      };

    case RECORDING_ACTIONS.SET_RECORDING_IN_PROGRESS:
      return {
        ...state,
        recordingInProgress: action.payload
      };

    case 'SET_HAS_ACTIVE_RECORDINGS':
      return {
        ...state,
        hasActiveRecordings: action.payload
      };

    default:
      return state;
  }
};

// Context
const RecordingSessionContext = createContext();

// Provider component
export const RecordingSessionProvider = ({ children }) => {
  const [state, dispatch] = useReducer(recordingSessionReducer, initialState);

  // Load recording counts from localStorage on mount
  useEffect(() => {
    console.log('🔄 INITIALIZATION: Starting RecordingSessionProvider initialization');

    try {
      const savedRecordingsCount = localStorage.getItem('icuAppRecordingsCount');
      if (savedRecordingsCount) {
        const parsedCounts = JSON.parse(savedRecordingsCount);

        // Update state with loaded counts - but don't trigger auto-advance yet
        Object.entries(parsedCounts).forEach(([phraseKey, count]) => {
          dispatch({
            type: RECORDING_ACTIONS.UPDATE_RECORDINGS_COUNT,
            payload: { phraseKey, count }
          });
        });

        console.log('📱 Recording counts loaded from localStorage:', parsedCounts);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load recording counts from localStorage:', error);
    }

    // Clear selected phrases on page refresh to ensure fresh sessions
    const isPageRefresh = !sessionStorage.getItem('icuAppSessionActive');

    if (isPageRefresh) {
      // Clear selected phrases for fresh session - use correct localStorage keys
      try {
        localStorage.removeItem('icu_selected_phrases'); // Correct key used by phraseRotationService
        localStorage.removeItem('icuAppSelectedPhrases'); // Legacy key cleanup
        console.log('🔄 Fresh session: Selected phrases cleared for new session');
      } catch (error) {
        console.warn('⚠️ Failed to clear selected phrases:', error);
      }
    } else {
      // Load selected phrases if session is already active (within-session navigation)
      const loadedPhrases = getSelectedPhrases();
      if (loadedPhrases && loadedPhrases.length > 0) {
        dispatch({
          type: RECORDING_ACTIONS.SET_SELECTED_PHRASES,
          payload: loadedPhrases
        });
        dispatch({
          type: RECORDING_ACTIONS.SET_PHRASES_SELECTED,
          payload: true
        });
        console.log('📱 Selected phrases loaded from localStorage for session persistence');
      }
    }

    // Mark session as active to prevent clearing on subsequent navigations
    sessionStorage.setItem('icuAppSessionActive', 'true');

    // CRITICAL FIX: Longer delay to ensure all state is settled before enabling auto-advance
    // This prevents false positive auto-advancement during component initialization
    setTimeout(() => {
      console.log('🔧 INITIALIZATION TIMEOUT: About to complete initialization');
      console.log('🔧 Current state before completion:', {
        showCompletionPrompt: state.showCompletionPrompt,
        phrasesSelected: state.phrasesSelected,
        selectedPhrases: state.selectedPhrases?.length || 0,
        currentPhraseIndex: state.currentPhraseIndex,
        hasActiveRecordings: state.hasActiveRecordings
      });

      // CRITICAL: Ensure completion prompt is false during initialization
      // This prevents the app from showing completion page when user first enters recording
      dispatch({
        type: RECORDING_ACTIONS.SET_COMPLETION_PROMPT,
        payload: false
      });

      // Reset active recordings flag on initialization
      dispatch({
        type: 'SET_HAS_ACTIVE_RECORDINGS',
        payload: false
      });

      dispatch({
        type: RECORDING_ACTIONS.SET_INITIALIZATION_COMPLETE
      });
      console.log('✅ INITIALIZATION COMPLETE: Auto-advancement now enabled after state settlement');
      console.log('✅ COMPLETION PROMPT: Reset to false to ensure recording page is visible');
    }, 500); // Increased delay to ensure all state is fully settled
  }, []);

  // Handle next phrase progression - this is the key function for auto-advance
  const handleNextPhrase = useCallback(() => {
    console.log('🚀 === HANDLE NEXT PHRASE CALLED ===');
    console.log('🔍 IMMEDIATE STATE CHECK:');
    console.log('  selectedPhrases:', state.selectedPhrases);
    console.log('  currentPhraseIndex:', state.currentPhraseIndex);
    console.log('  recordingsCount:', state.recordingsCount);
    console.log('  isInitializing:', state.isInitializing);

    // CRITICAL FIX: Prevent handleNextPhrase during initialization
    if (state.isInitializing) {
      console.log('🔄 HANDLE NEXT PHRASE: Blocked during initialization - preventing false completion prompt');
      return;
    }

    if (!state.selectedPhrases || state.selectedPhrases.length === 0) {
      console.log('❌ No phrases selected');
      return;
    }

    // Check if this is the last phrase
    if (state.currentPhraseIndex >= state.selectedPhrases.length - 1) {
      console.log('🏁 LAST PHRASE: Reached end of selected phrases');

      // CRITICAL FIX: Only show completion prompt if user has actually made recordings
      // This prevents showing completion immediately when entering recording workflow
      if (state.hasActiveRecordings) {
        console.log('🏁 END OF PHRASE LIST: User has recordings, showing completion prompt');
        console.log('📊 DATA COLLECTION MODE: Users can restart to record same phrases again');

        dispatch({
          type: RECORDING_ACTIONS.SET_COMPLETION_PROMPT,
          payload: true
        });
      } else {
        console.log('🏁 END OF PHRASE LIST: No recordings yet, staying on recording page');
        console.log('📝 User needs to make at least one recording before completion');
      }
    } else {
      console.log('📝 ADVANCING TO NEXT PHRASE');
      const nextPhraseIndex = state.currentPhraseIndex + 1;
      const nextPhrase = state.selectedPhrases[nextPhraseIndex];

      console.log('📝 Next phrase details:', {
        nextPhraseIndex,
        nextPhrase: nextPhrase?.phrase,
        nextCategory: nextPhrase?.category
      });

      // Update phrase index
      dispatch({
        type: RECORDING_ACTIONS.SET_CURRENT_PHRASE_INDEX,
        payload: nextPhraseIndex
      });

      // Update category if needed
      if (nextPhrase.category !== state.selectedCategory) {
        console.log('📝 Updating category from', state.selectedCategory, 'to', nextPhrase.category);
        dispatch({
          type: RECORDING_ACTIONS.SET_SELECTED_CATEGORY,
          payload: nextPhrase.category
        });
      }

      // Set recording number for next phrase (continue counting from existing recordings)
      const nextPhraseKey = `${nextPhrase.category}:${nextPhrase.phrase}`;
      const existingRecordings = state.recordingsCount[nextPhraseKey] || 0;
      console.log('📝 Setting recording number to', existingRecordings + 1, '(continuing from', existingRecordings, 'existing recordings)');
      dispatch({
        type: RECORDING_ACTIONS.SET_CURRENT_RECORDING_NUMBER,
        payload: existingRecordings + 1
      });
    }
  }, [state.selectedPhrases, state.currentPhraseIndex, state.recordingsCount, state.RECORDINGS_PER_PHRASE, state.selectedCategory, state.isInitializing]);

  // AUTO-ADVANCE EFFECT - watches for recording count changes and triggers advancement
  // This effect should ONLY trigger when a recording is actually completed, not during initialization
  useEffect(() => {
    console.log('🔄 AUTO-ADVANCE EFFECT TRIGGERED:', {
      hasSelectedPhrases: !!state.selectedPhrases,
      phrasesLength: state.selectedPhrases?.length || 0,
      currentPhraseIndex: state.currentPhraseIndex,
      recordingsCount: state.recordingsCount,
      isInitializing: state.isInitializing,
      phrasesSelected: state.phrasesSelected,
      timestamp: new Date().toISOString()
    });

    // CRITICAL FIX: Prevent auto-advancement during initialization
    if (state.isInitializing) {
      console.log('🔄 AUTO-ADVANCE: Blocked during initialization - preventing false positive advancement');
      return;
    }

    // CRITICAL FIX: Only auto-advance if phrases have been actively selected in this session
    // This prevents auto-advance from triggering when loading existing localStorage data
    if (!state.phrasesSelected) {
      console.log('🔄 AUTO-ADVANCE: Blocked - phrases not actively selected in this session');
      return;
    }

    if (!state.selectedPhrases || state.selectedPhrases.length === 0 || state.currentPhraseIndex < 0) {
      console.log('🔄 AUTO-ADVANCE: Early return - no phrases or invalid index');
      return;
    }

    const currentPhraseObj = state.selectedPhrases[state.currentPhraseIndex];
    if (!currentPhraseObj) {
      console.log('🔄 AUTO-ADVANCE: Early return - no current phrase object');
      return;
    }

    const phraseKey = `${currentPhraseObj.category}:${currentPhraseObj.phrase}`;
    const currentCount = state.recordingsCount[phraseKey] || 0;

    console.log('🔄 AUTO-ADVANCE CHECK:', {
      phrase: currentPhraseObj.phrase,
      phraseKey,
      currentCount,
      required: state.RECORDINGS_PER_PHRASE,
      shouldAdvance: currentCount >= state.RECORDINGS_PER_PHRASE
    });

    // CRITICAL FIX: Allow unlimited re-recording for data collection
    // Auto-advance after every 3 recordings, regardless of existing counts
    // This supports multiple recording sessions of the same phrases
    if (currentCount > 0 && currentCount % state.RECORDINGS_PER_PHRASE === 0) {
      console.log('🎯 AUTO-ADVANCE: Phrase milestone reached (multiple of 3 recordings), calling handleNextPhrase');
      console.log('📊 DATA COLLECTION MODE: Allowing unlimited re-recording of phrases');

      // Use a small delay to ensure state is fully updated
      setTimeout(() => {
        console.log('🚀 AUTO-ADVANCE: Executing handleNextPhrase');
        handleNextPhrase();
      }, 100);
    }
  }, [state.recordingsCount, state.currentPhraseIndex, state.selectedPhrases, state.RECORDINGS_PER_PHRASE, state.isInitializing, state.phrasesSelected, handleNextPhrase]);

  // Action creators
  const actions = {
    setSelectedPhrases: useCallback((phrases) => {
      console.log('🎯 SET SELECTED PHRASES: Setting phrases and resetting completion state');
      dispatch({
        type: RECORDING_ACTIONS.SET_SELECTED_PHRASES,
        payload: phrases
      });
      dispatch({
        type: RECORDING_ACTIONS.SET_PHRASES_SELECTED,
        payload: true
      });
      // Reset completion prompt when new phrases are selected
      dispatch({
        type: RECORDING_ACTIONS.SET_COMPLETION_PROMPT,
        payload: false
      });
      // Reset active recordings flag for new phrase selection
      dispatch({
        type: 'SET_HAS_ACTIVE_RECORDINGS',
        payload: false
      });
      saveSelectedPhrases(phrases);
      console.log('✅ Phrases set, completion prompt reset, ready for recording');
    }, []),
    
    recordingCompleted: useCallback((metadata) => {
      console.log('📹 RECORDING COMPLETED FUNCTION CALLED:', {
        metadata,
        currentState: {
          currentPhraseIndex: state.currentPhraseIndex,
          selectedPhrases: state.selectedPhrases?.map(p => p.phrase),
          recordingsCount: state.recordingsCount
        }
      });

      const phraseKey = `${metadata.category}:${metadata.phrase}`;
      const currentCount = state.recordingsCount[phraseKey] || 0;
      const newCount = currentCount + 1;

      console.log('📹 RECORDING COMPLETED - COUNT UPDATE:', {
        phrase: metadata.phrase,
        category: metadata.category,
        phraseKey,
        previousCount: currentCount,
        newCount,
        willTriggerAutoAdvance: newCount >= state.RECORDINGS_PER_PHRASE
      });

      console.log(`  ✅ Recording ${newCount}/${state.RECORDINGS_PER_PHRASE} completed for phrase: ${metadata.phrase}`);

      // Mark that phrases have been actively selected and recordings made
      if (!state.phrasesSelected) {
        dispatch({
          type: RECORDING_ACTIONS.SET_PHRASES_SELECTED,
          payload: true
        });
      }

      // Mark that user has made active recordings in this session
      if (!state.hasActiveRecordings) {
        dispatch({
          type: 'SET_HAS_ACTIVE_RECORDINGS',
          payload: true
        });
        console.log('📹 First recording completed - user now has active recordings');
      }

      // Update recording count - this will trigger auto-advance useEffect
      dispatch({
        type: RECORDING_ACTIONS.UPDATE_RECORDINGS_COUNT,
        payload: { phraseKey, count: newCount }
      });

      // Update current recording number (should be the new count, not +1)
      dispatch({
        type: RECORDING_ACTIONS.SET_CURRENT_RECORDING_NUMBER,
        payload: newCount
      });

      // Mark recording as no longer in progress
      dispatch({
        type: RECORDING_ACTIONS.SET_RECORDING_IN_PROGRESS,
        payload: false
      });

      // Check if phrase milestone is reached (every 3 recordings)
      if (newCount > 0 && newCount % state.RECORDINGS_PER_PHRASE === 0) {
        console.log(`  🎯 PHRASE MILESTONE REACHED - ${newCount} total recordings (${newCount / state.RECORDINGS_PER_PHRASE} sets of ${state.RECORDINGS_PER_PHRASE})`);
        console.log('    Current phrase:', state.selectedPhrases?.[state.currentPhraseIndex]?.phrase);
        console.log('    Current phrase index:', state.currentPhraseIndex);
        console.log('    Total selected phrases:', state.selectedPhrases?.length);
        console.log('    Is last phrase?', state.currentPhraseIndex >= (state.selectedPhrases?.length - 1));
        console.log('    Auto-advancement will be handled by useEffect watching recordingsCount');
        console.log('📊 DATA COLLECTION MODE: Allowing unlimited re-recording for maximum data collection');
      }

      console.log('📹 RECORDING COMPLETED - DISPATCHES SENT, useEffect should trigger now');
    }, [state.recordingsCount, state.currentPhraseIndex, state.selectedPhrases, state.RECORDINGS_PER_PHRASE, state.phrasesSelected]),
    
    setCurrentPhraseIndex: useCallback((index) => {
      dispatch({
        type: RECORDING_ACTIONS.SET_CURRENT_PHRASE_INDEX,
        payload: index
      });
    }, []),
    
    setCompletionPrompt: useCallback((show) => {
      dispatch({
        type: RECORDING_ACTIONS.SET_COMPLETION_PROMPT,
        payload: show
      });
    }, []),
    
    resetSession: useCallback(() => {
      dispatch({ type: RECORDING_ACTIONS.RESET_RECORDING_SESSION });
    }, []),

    setRecordingInProgress: useCallback((inProgress) => {
      dispatch({
        type: RECORDING_ACTIONS.SET_RECORDING_IN_PROGRESS,
        payload: inProgress
      });
    }, [])
  };

  // Computed values
  const currentPhrase = state.selectedPhrases && state.selectedPhrases[state.currentPhraseIndex]
    ? state.selectedPhrases[state.currentPhraseIndex].phrase
    : '';

  const value = {
    ...state,
    ...actions,
    currentPhrase,
    handleNextPhrase
  };

  return (
    <RecordingSessionContext.Provider value={value}>
      {children}
    </RecordingSessionContext.Provider>
  );
};

// Hook to use recording session
export const useRecordingSession = () => {
  const context = useContext(RecordingSessionContext);
  if (!context) {
    throw new Error('useRecordingSession must be used within a RecordingSessionProvider');
  }
  return context;
};

export default RecordingSessionProvider;
